2025-08-29 01:30:23 UTC
Windows-10-10.0.26100
Ren'Py 8.2.1.24030407

Trap Resort
0.4
Built at 2025-08-13 17:10:03 UTC

Early init took 0.06s
Loading error handling took 0.05s
Loading script took 0.92s
Loading save slot metadata took 0.05s
Loading persistent took 0.00s
Set script version to: (8, 2, 1)
Running init code took 0.13s
Loading analysis data took 0.01s
Analyze and compile ATL took 0.00s
Reloading save slot metadata took 0.03s
Index archives took 0.00s
Dump and make backups took 0.00s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.05s
DPI scale factor: 1.250000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Creating interface object took 0.26s
Cleaning stores took 0.00s
Init translation took 0.04s
Build styles took 0.00s
Load screen analysis took 0.02s
Analyze screens took 0.00s
Save screen analysis took 0.00s
Prepare screens took 0.06s
Save pyanalysis. took 0.00s
Save bytecode. took 0.00s
Running _start took 0.00s
Interface start took 0.71s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Fullscreen mode.
Vendor: "b'NVIDIA Corporation'"
Renderer: b'NVIDIA GeForce RTX 4060 Laptop GPU/PCIe/SSE2'
Version: b'4.6.0 NVIDIA 566.49'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1920, 1080) drawable=(1920, 1080)
Could not open 'cache/shaders.txt':
Maximum texture size: 4096x4096
